<script lang="ts">
  import { onMount } from 'svelte';
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Carousel from '$lib/components/ui/carousel/index.js';

  import Autoplay from 'embla-carousel-autoplay';
  import type { AlignmentOptionType } from 'embla-carousel/components/Alignment';
  import { Skeleton } from '$lib/components/ui/skeleton';
  interface FeaturedCompany {
    id: string;
    name: string;
    logoUrl: string | null;
    companySize: string | null;
    companyStage: string | null;
    activeJobCount: number | null;
  }

  interface CategorizedCompanies {
    startups: FeaturedCompany[];
    growth: FeaturedCompany[];
    enterprise: FeaturedCompany[];
  }

  // Reactive state using Svelte 5 patterns
  let companies = $state<CategorizedCompanies>({
    startups: [],
    growth: [],
    enterprise: [],
  });
  let loading = $state(true);

  // Create autoplay plugins with different settings for each carousel
  const plugin1 = Autoplay({ delay: 2000, stopOnInteraction: true });
  const plugin2 = Autoplay({ delay: 4000, stopOnInteraction: true });
  const plugin3 = Autoplay({ delay: 2500, stopOnInteraction: true });

  // Set reverse direction for the middle carousel
  const options1 = { align: 'start' as AlignmentOptionType, loop: true };
  const options2 = { align: 'start' as AlignmentOptionType, loop: true };
  const options3 = { align: 'start' as const, loop: true };

  // Fetch companies data
  async function fetchCompanies() {
    try {
      console.log('🔄 Fetching companies...');
      const response = await fetch('/api/companies/featured');
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Companies fetched:', data);
        console.log('📊 Startups count:', data.startups?.length || 0);
        console.log('📊 Growth count:', data.growth?.length || 0);
        console.log('📊 Enterprise count:', data.enterprise?.length || 0);

        // Log first few companies to check logo URLs
        if (data.startups?.length > 0) {
          console.log(
            '🏢 Sample startup logos:',
            data.startups
              .slice(0, 3)
              .map((c: FeaturedCompany) => ({ name: c.name, logoUrl: c.logoUrl }))
          );
        }

        // Log all logo URLs to debug the format
        const allCompanies = [
          ...(data.startups || []),
          ...(data.growth || []),
          ...(data.enterprise || []),
        ];
        console.log(
          '🔍 All logo URLs:',
          allCompanies.map((c) => ({ name: c.name, logoUrl: c.logoUrl }))
        );

        companies = data;
      } else {
        console.error(
          '❌ Failed to fetch featured companies:',
          response.status,
          response.statusText
        );
      }
    } catch (error) {
      console.error('💥 Error fetching featured companies:', error);
    } finally {
      loading = false;
      console.log('📊 Final companies state:', companies);
    }
  }

  // Initialize data fetch on client side only
  onMount(() => {
    fetchCompanies();
  });

  function getCompanyLogo(company: FeaturedCompany): string {
    // If company has a logoUrl, process it through the worker for proper CORS
    if (company.logoUrl) {
      console.log(`🔍 Processing logo for ${company.name}: ${company.logoUrl}`);

      // Check if it's a Clearbit URL - use directly (has proper CORS)
      if (company.logoUrl.includes('logo.clearbit.com')) {
        console.log(`✅ Using Clearbit URL directly for ${company.name}`);
        return company.logoUrl;
      }

      // Check if it's already a worker URL - use directly
      if (
        company.logoUrl.includes('hirli-static-assets.christopher-eugene-rodriguez.workers.dev')
      ) {
        console.log(`✅ Already worker URL for ${company.name}: ${company.logoUrl}`);
        return company.logoUrl;
      }

      // Handle R2 URLs - extract the path after 'logos/'
      let logoPath = company.logoUrl;

      // If it's a direct R2 URL, extract the path
      if (logoPath.includes('pub-') && logoPath.includes('.r2.dev/')) {
        logoPath = logoPath.split('.r2.dev/')[1];
      }

      // Fix double logos/ prefix issue
      if (logoPath.startsWith('logos/logos/')) {
        logoPath = logoPath.replace('logos/logos/', '');
      } else if (logoPath.startsWith('logos/')) {
        logoPath = logoPath.replace('logos/', '');
      }

      // Always use Worker URL for proper CORS support
      const workerUrl = 'https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev';
      const finalUrl = `${workerUrl}/logos/${logoPath}`;
      console.log(`🔄 Converting to worker URL for ${company.name}: ${finalUrl}`);
      return finalUrl;
    }

    // No logo URL - show placeholder with company name
    console.log(`📝 Using placeholder for ${company.name}`);
    return `https://placehold.co/200x80/f1f5f9/64748b?text=${encodeURIComponent(company.name)}`;
  }

  function handleImageError(event: Event, company: FeaturedCompany) {
    const img = event.target as HTMLImageElement;
    console.warn(`⚠️ Logo failed to load for ${company.name}`);
    console.warn(`❌ Failed URL: ${img.src}`);
    console.warn(`📋 Original logoUrl: ${company.logoUrl}`);

    // Use placeholder fallback
    img.src = `https://placehold.co/200x80/f1f5f9/64748b?text=${encodeURIComponent(company.name)}`;
  }
</script>

<section class="bg-muted py-16">
  <div class="container mx-auto px-4">
    <!-- Section Header -->
    <div class="mb-12 text-center">
      <h2 class="text-foreground text-3xl font-bold tracking-tight sm:text-4xl">
        Trusted by Leading Companies
      </h2>
      <p class="text-muted-foreground mt-4 text-lg">
        Join thousands of professionals working at innovative companies worldwide
      </p>
    </div>

    {#if loading}
      <div class="flex flex-col gap-4">
        <div class="flex grid grid-cols-5 items-center justify-center gap-4">
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
        </div>
        <div class="flex grid grid-cols-5 items-center justify-center gap-4">
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
        </div>
        <div class="flex grid grid-cols-5 items-center justify-center gap-4">
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
          <Skeleton class="bg-primary/10 h-30 w-full" />
        </div>
      </div>
    {:else if companies.startups.length > 0 || companies.growth.length > 0 || companies.enterprise.length > 0}
      <!-- Company Carousels -->
      <div class="flex flex-col gap-6">
        <!-- Startups Carousel -->
        {#if companies.startups.length > 0}
          <div class="space-y-2">
            <h3 class="text-muted-foreground text-sm font-medium uppercase tracking-wider">
              Startups & Early Stage
            </h3>
            <Carousel.Root plugins={[plugin1]} opts={options1} class="w-full">
              <Carousel.Content class="-ml-2 md:-ml-4">
                {#each companies.startups as company (company.id)}
                  <Carousel.Item class="basis-1/3 pl-2 md:basis-1/4 md:pl-4 lg:basis-1/6">
                    <div class="group">
                      <Card.Root
                        class="border-border/50 bg-background/50 hover:border-primary/20 hover:bg-background/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
                        <Card.Content class="flex h-20 items-center justify-center p-4">
                          <img
                            src={getCompanyLogo(company)}
                            alt="{company.name} logo"
                            class="max-h-10 max-w-[100px] object-contain opacity-90 grayscale-0 filter transition-all duration-300 group-hover:scale-105 group-hover:opacity-100"
                            loading="lazy"
                            onerror={(e) => handleImageError(e, company)} />
                        </Card.Content>
                      </Card.Root>
                    </div>
                  </Carousel.Item>
                {/each}
              </Carousel.Content>
            </Carousel.Root>
          </div>
        {/if}
        <!-- Growth Companies Carousel -->
        {#if companies.growth.length > 0}
          <div class="space-y-2">
            <h3 class="text-muted-foreground text-sm font-medium uppercase tracking-wider">
              Growth & Scale-up
            </h3>
            <Carousel.Root plugins={[plugin2]} opts={options2} class="w-full">
              <Carousel.Content class="-ml-2 md:-ml-4">
                {#each companies.growth as company (company.id)}
                  <Carousel.Item class="basis-1/3 pl-2 md:basis-1/4 md:pl-4 lg:basis-1/6">
                    <div class="group">
                      <Card.Root
                        class="border-border/50 bg-background/50 hover:border-primary/20 hover:bg-background/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
                        <Card.Content class="flex h-20 items-center justify-center p-4">
                          <img
                            src={getCompanyLogo(company)}
                            alt="{company.name} logo"
                            class="max-h-10 max-w-[100px] object-contain opacity-70 grayscale filter transition-all duration-300 group-hover:scale-105 group-hover:opacity-100 group-hover:grayscale-0"
                            loading="lazy"
                            onerror={(e) => handleImageError(e, company)} />
                        </Card.Content>
                      </Card.Root>
                    </div>
                  </Carousel.Item>
                {/each}
              </Carousel.Content>
            </Carousel.Root>
          </div>
        {/if}
        <!-- Enterprise Companies Carousel -->
        {#if companies.enterprise.length > 0}
          <div class="space-y-2">
            <h3 class="text-muted-foreground text-sm font-medium uppercase tracking-wider">
              Enterprise & Fortune 500
            </h3>
            <Carousel.Root plugins={[plugin3]} opts={options3} class="w-full">
              <Carousel.Content class="-ml-2 md:-ml-4">
                {#each companies.enterprise as company (company.id)}
                  <Carousel.Item class="basis-1/3 pl-2 md:basis-1/4 md:pl-4 lg:basis-1/6">
                    <div class="group">
                      <Card.Root
                        class="border-border/50 bg-background/50 hover:border-primary/20 hover:bg-background/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
                        <Card.Content class="flex h-20 items-center justify-center p-4">
                          <img
                            src={getCompanyLogo(company)}
                            alt="{company.name} logo"
                            class="max-h-10 max-w-[100px] object-contain opacity-70 grayscale filter transition-all duration-300 group-hover:scale-105 group-hover:opacity-100 group-hover:grayscale-0"
                            loading="lazy"
                            onerror={(e) => handleImageError(e, company)} />
                        </Card.Content>
                      </Card.Root>
                    </div>
                  </Carousel.Item>
                {/each}
              </Carousel.Content>
            </Carousel.Root>
          </div>
        {/if}
      </div>

      <!-- Bottom CTA -->
      <div class="mt-12 text-center">
        <p class="text-muted-foreground mb-4 text-lg">
          Join thousands of professionals working at these amazing companies
        </p>
        <div class="text-muted-foreground flex flex-wrap justify-center gap-6 text-sm">
          <div class="flex items-center gap-2">
            <div class="h-2 w-2 rounded-full bg-green-500"></div>
            <span>Remote-first opportunities</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="h-2 w-2 rounded-full bg-blue-500"></div>
            <span>Competitive salaries</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="h-2 w-2 rounded-full bg-purple-500"></div>
            <span>Growth opportunities</span>
          </div>
        </div>
      </div>
    {:else}
      <!-- No companies with logos available -->
      <div class="py-12 text-center">
        <p class="text-muted-foreground text-lg">
          Company logos are being processed and will appear here soon.
        </p>
        <p class="text-muted-foreground mt-2 text-sm">
          Our system is currently downloading and optimizing company logos from various sources.
        </p>
      </div>
    {/if}
  </div>
</section>
