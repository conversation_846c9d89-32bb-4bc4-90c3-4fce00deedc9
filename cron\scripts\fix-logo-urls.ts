import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

async function fixLogoUrls() {
  logger.info("🔧 Starting logo URL cleanup...");

  try {
    // Get all companies with logoUrl containing logos/ prefix
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: {
          not: null,
          contains: "logos/",
        },
      },
      select: {
        id: true,
        name: true,
        logoUrl: true,
      },
    });

    logger.info(`📊 Found ${companies.length} companies with logos/ in URL`);

    let fixedCount = 0;
    let skippedCount = 0;

    for (const company of companies) {
      if (!company.logoUrl) continue;

      let originalUrl = company.logoUrl;
      let fixedUrl = originalUrl;

      logger.info(`🔍 Processing ${company.name}: ${originalUrl}`);

      // Skip Clearbit URLs - they're fine
      if (originalUrl.includes("logo.clearbit.com")) {
        logger.info(`   ⏭️ Skipping Clearbit URL`);
        skippedCount++;
        continue;
      }

      // Handle worker URLs with double logos/ prefix
      if (
        originalUrl.includes(
          "hirli-static-assets.christopher-eugene-rodriguez.workers.dev"
        )
      ) {
        // Check if worker URL has double logos/ prefix
        if (originalUrl.includes("/logos/logos/")) {
          fixedUrl = originalUrl.replace("/logos/logos/", "/");
          logger.info(`   🔧 Fixing worker URL with double logos/ prefix`);
        }
        // Check if worker URL has single logos/ prefix
        else if (originalUrl.includes('/logos/')) {
          fixedUrl = originalUrl.replace('/logos/', '/');
          logger.info(`   🔧 Fixing worker URL with single logos/ prefix`);
        } else {
          logger.info(`   ⏭️ Worker URL is fine`);
          skippedCount++;
          continue;
        }
      }
      // Handle non-worker URLs
      else {
        // Fix double logos/logos/ prefix
        if (originalUrl.includes("logos/logos/")) {
          fixedUrl = originalUrl.replace("logos/logos/", "");
          logger.info(`   🔧 Removing double logos/ prefix`);
        }
        // Fix single logos/ prefix
        else if (originalUrl.includes("logos/")) {
          // Extract just the filename after logos/
          const parts = originalUrl.split("logos/");
          if (parts.length > 1) {
            fixedUrl = parts[parts.length - 1]; // Get the last part (filename)
            logger.info(`   🔧 Removing single logos/ prefix`);
          }
        }
      }

      // Only update if URL actually changed
      if (fixedUrl !== originalUrl) {
        await prisma.company.update({
          where: { id: company.id },
          data: { logoUrl: fixedUrl },
        });

        logger.info(`✅ Fixed ${company.name}:`);
        logger.info(`   Before: ${originalUrl}`);
        logger.info(`   After:  ${fixedUrl}`);
        fixedCount++;
      } else {
        logger.info(`   ⏭️ No change needed`);
        skippedCount++;
      }
    }

    logger.info(`🎉 Logo URL cleanup completed:`);
    logger.info(`   ✅ Fixed: ${fixedCount}`);
    logger.info(`   ⏭️  Skipped: ${skippedCount}`);
  } catch (error) {
    logger.error("❌ Error fixing logo URLs:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
fixLogoUrls().catch(console.error);
